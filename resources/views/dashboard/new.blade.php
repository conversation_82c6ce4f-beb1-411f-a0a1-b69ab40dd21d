@extends('layouts.dashboard-new')

@section('title', 'Dashboard')

@section('content')
    <div class="dashboard-container">
        <h1 class="page-title">All Recent Activity</h1>

        <div class="activity-card">
            <div class="card-header">
                <h3 class="card-title">Recent Requests</h3>
                <a href="{{ route('issue.index') }}" class="btn btn-primary">View All</a>
            </div>
        <div class="card-body">
            @if(isset($recentIssues) && $recentIssues->count() > 0)
                @foreach($recentIssues as $issue)
                    <div class="activity-item">
                        <div class="activity-avatar">
                            @if($issue->user && $issue->user->avatar_url)
                                <img src="{{ $issue->user->avatar_url }}" alt="{{ $issue->user->firstname }} {{ $issue->user->lastname }}">
                            @else
                                <img src="{{ asset('images/profile-placeholder.png') }}" alt="User">
                            @endif
                        </div>
                        <div class="activity-content">
                            <div class="activity-header">
                                <h3 class="activity-title">{{ $issue->title }}</h3>
                                <span class="activity-time">{{ $issue->created_at->diffForHumans() }}</span>
                            </div>
                            <p class="activity-group">
                                Request by {{ $issue->user ? $issue->user->firstname . ' ' . $issue->user->lastname : 'Unknown User' }}
                                in {{ $issue->group ? $issue->group->name : 'Unknown Group' }}
                            </p>
                            @if($issue->amount)
                                <p class="activity-group"><strong>Amount: ${{ number_format($issue->amount, 2) }}</strong></p>
                            @endif
                            <p class="activity-description">
                                {{ \Illuminate\Support\Str::limit($issue->description ?? '', 300) }}
                            </p>
                            <div class="activity-actions">
                                <a href="{{ route('issue.show', $issue->id) }}" class="activity-action">
                                    <i class="fas fa-eye"></i>
                                    <span>View Details</span>
                                </a>
                                <a href="#" class="activity-action">
                                    <i class="fas fa-check"></i>
                                    <span>I Concur</span>
                                </a>
                                <a href="#" class="activity-action">
                                    <i class="fas fa-door-open"></i>
                                    <span>I Am Open</span>
                                </a>
                                <a href="#" class="activity-action">
                                    <i class="fas fa-question"></i>
                                    <span>I Wonder</span>
                                </a>
                            </div>
                            <form class="comment-form" action="{{ route('issue.post', $issue->id) }}" method="POST">
                                @csrf
                                <div class="comment-box">
                                    <input type="text" name="body" class="comment-input" placeholder="Type Comment...">
                                    <button type="submit" class="comment-button">
                                        <i class="fas fa-arrow-right"></i>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                @endforeach
            @else
                <div class="empty-state">
                    <p>No recent requests found.</p>
                    <a href="{{ route('issue.create') }}" class="btn btn-primary">Create Request</a>
                </div>
            @endif
        </div>
    </div>

    <div class="activity-card">
        <div class="card-header">
            <h3 class="card-title">Recent Discussions</h3>
            <a href="{{ route('discussion.index') }}" class="btn btn-primary">View All</a>
        </div>
        <div class="card-body">
            @if(isset($recentDiscussions) && $recentDiscussions->count() > 0)
                @foreach($recentDiscussions as $discussion)
                    <div class="activity-item">
                        <div class="activity-avatar">
                            @if($discussion->user && $discussion->user->avatar_url)
                                <img src="{{ $discussion->user->avatar_url }}" alt="{{ $discussion->user->firstname }} {{ $discussion->user->lastname }}">
                            @else
                                <img src="{{ asset('images/profile-placeholder.png') }}" alt="User">
                            @endif
                        </div>
                        <div class="activity-content">
                            <div class="activity-header">
                                <h3 class="activity-title">{{ $discussion->title }}</h3>
                                <span class="activity-time">{{ $discussion->created_at->diffForHumans() }}</span>
                            </div>
                            <p class="activity-group">
                                Discussion by {{ $discussion->user ? $discussion->user->firstname . ' ' . $discussion->user->lastname : 'Unknown User' }}
                                in {{ $discussion->group ? $discussion->group->name : 'Unknown Group' }}
                            </p>
                            <p class="activity-description">
                                {{ \Illuminate\Support\Str::limit($discussion->body ?? '', 300) }}
                            </p>
                            <div class="activity-actions">
                                <a href="{{ route('discussion.show', $discussion->id) }}" class="activity-action">
                                    <i class="fas fa-eye"></i>
                                    <span>View Details</span>
                                </a>
                                <a href="#" class="activity-action">
                                    <i class="fas fa-comment"></i>
                                    <span>Comment</span>
                                </a>
                            </div>
                            <form class="comment-form" action="{{ route('discussion.post', $discussion->id) }}" method="POST">
                                @csrf
                                <div class="comment-box">
                                    <input type="text" name="body" class="comment-input" placeholder="Type Comment...">
                                    <button type="submit" class="comment-button">
                                        <i class="fas fa-arrow-right"></i>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                @endforeach
            @else
                <div class="empty-state">
                    <p>No recent discussions found.</p>
                    <a href="{{ route('discussion.create') }}" class="btn btn-primary">Start Discussion</a>
                </div>
            @endif
        </div>
    </div>
    </div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Example of Laravel-style JavaScript
        const commentForms = document.querySelectorAll('.comment-form');

        commentForms.forEach(form => {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                const input = this.querySelector('.comment-input');
                const comment = input.value.trim();

                if (comment) {
                    // In a real app, you would send an AJAX request to save the comment
                    console.log('Submitting comment:', comment);

                    // Clear the input
                    input.value = '';

                    // Show a success message (in a real app)
                    // Example: Swal.fire('Success', 'Your comment has been posted', 'success');
                }
            });
        });
    });
</script>
@endsection
