@extends('layouts.dashboard-new')

@section('title', 'Dashboard')

@push('styles')
<style>
    .activity-card {
        background: #fff;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        margin-bottom: 32px;
        padding: 0;
    }
    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0;
        padding: 24px 32px 18px 32px;
        border-bottom: 1px solid #F5F6F7;
    }
    .card-title {
        font-size: 1.3rem;
        font-weight: 600;
        color: #5144A1;
        margin: 0;
    }
    .activity-item {
        display: flex;
        gap: 24px;
        border-bottom: 1px solid #F5F6F7;
        padding: 24px 32px;
    }
    .activity-item:last-child {
        border-bottom: none;
    }
    .activity-avatar img {
        width: 50px;
        height: 50px;
        border-radius: 8px;
        object-fit: cover;
        background: #F5F6F7;
    }
    .activity-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 8px;
    }
    .activity-header {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        margin-bottom: 8px;
    }
    .activity-title {
        font-size: 18px;
        font-weight: 600;
        color: #4D5E80;
        margin: 0;
        line-height: 1.3;
    }
    .activity-time {
        color: #6B7280;
        font-size: 14px;
        white-space: nowrap;
        margin-left: 16px;
    }
    .activity-group {
        color: #6B7280;
        font-size: 14px;
        margin: 0 0 12px 0;
        line-height: 1.4;
    }
    .activity-description {
        color: #4D5E80;
        font-size: 14px;
        line-height: 1.5;
        margin-bottom: 16px;
    }
    .request-amount {
        background: #E8F5E8;
        color: #2D5A2D;
        padding: 4px 12px;
        border-radius: 16px;
        font-size: 12px;
        font-weight: 600;
        display: inline-block;
        margin-bottom: 12px;
    }
    .voting-actions {
        display: flex;
        gap: 12px;
        margin-bottom: 16px;
        flex-wrap: wrap;
    }
    .vote-btn {
        display: flex;
        align-items: center;
        gap: 6px;
        padding: 8px 16px;
        border: 1px solid #E5E7EB;
        border-radius: 20px;
        background: #fff;
        color: #6B7280;
        text-decoration: none;
        font-size: 14px;
        transition: all 0.2s ease;
        cursor: pointer;
    }
    .vote-btn:hover {
        background: #F3F4F6;
        border-color: #D1D5DB;
        color: #374151;
    }
    .vote-btn.active {
        background: #5144A1;
        border-color: #5144A1;
        color: white;
    }
    .vote-btn i {
        font-size: 14px;
    }
    .comment-section {
        margin-top: 16px;
    }
    .recent-comments {
        margin-bottom: 16px;
    }
    .comment-item {
        display: flex;
        gap: 12px;
        margin-bottom: 12px;
        padding: 12px;
        background: #F9FAFB;
        border-radius: 8px;
    }
    .comment-avatar img {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        object-fit: cover;
    }
    .comment-content {
        flex: 1;
    }
    .comment-author {
        font-weight: 600;
        color: #374151;
        font-size: 14px;
        margin-bottom: 4px;
    }
    .comment-text {
        color: #6B7280;
        font-size: 14px;
        line-height: 1.4;
    }
    .comment-time {
        color: #9CA3AF;
        font-size: 12px;
        margin-top: 4px;
    }
    .comment-form {
        display: flex;
        gap: 12px;
        align-items: center;
    }
    .comment-input {
        flex: 1;
        padding: 12px 16px;
        border: 1px solid #E5E7EB;
        border-radius: 24px;
        font-size: 14px;
        outline: none;
        transition: border-color 0.2s ease;
    }
    .comment-input:focus {
        border-color: #5144A1;
    }
    .comment-button {
        background: #5144A1;
        color: white;
        border: none;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: background-color 0.2s ease;
    }
    .comment-button:hover {
        background: #4535a0;
    }
    .btn-primary {
        background: #5144A1;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 6px;
        text-decoration: none;
        font-size: 14px;
        font-weight: 500;
        transition: background-color 0.2s ease;
    }
    .btn-primary:hover {
        background: #4535a0;
        color: white;
    }
    .empty-state {
        text-align: center;
        padding: 48px 32px;
        color: #6B7280;
    }
</style>
@endpush

@section('content')
    <div class="dashboard-container">
        <h1 class="page-title">All Recent Activity</h1>

        <div class="activity-card">
            <div class="card-header">
                <h3 class="card-title">Recent Requests</h3>
                <a href="{{ route('issue.index') }}" class="btn btn-primary">View All</a>
            </div>
            <div class="card-body">
                @if(isset($recentIssues) && $recentIssues->count() > 0)
                    @foreach($recentIssues as $issue)
                        <div class="activity-item">
                            <div class="activity-avatar">
                                @if($issue->creator && $issue->creator->profile_pic && $issue->creator->profile_pic !== 'profile-placeholder.png')
                                    <img src="{{ asset('images/' . $issue->creator->profile_pic) }}" alt="{{ $issue->creator->firstname }} {{ $issue->creator->lastname }}">
                                @else
                                    <img src="{{ asset('images/profile-placeholder.png') }}" alt="User">
                                @endif
                            </div>
                            <div class="activity-content">
                                <div class="activity-header">
                                    <h3 class="activity-title">{{ $issue->title }}</h3>
                                    <span class="activity-time">{{ $issue->created_at->diffForHumans() }}</span>
                                </div>
                                <p class="activity-group">
                                    Request by {{ $issue->creator ? $issue->creator->firstname . ' ' . $issue->creator->lastname : 'Unknown User' }}
                                    in {{ $issue->discussion && $issue->discussion->group ? $issue->discussion->group->name : 'Unknown Group' }}
                                </p>
                                @if(isset($issue->amount) && $issue->amount)
                                    <div class="request-amount">Request Amount: ${{ number_format($issue->amount, 2) }}</div>
                                @endif
                                <p class="activity-description">
                                    {{ \Illuminate\Support\Str::limit($issue->description ?? '', 300) }}
                                </p>

                                <div class="voting-actions">
                                    <button class="vote-btn" data-vote="concur" data-issue="{{ $issue->id }}">
                                        <i class="fas fa-check"></i>
                                        <span>I Concur</span>
                                    </button>
                                    <button class="vote-btn" data-vote="open" data-issue="{{ $issue->id }}">
                                        <i class="fas fa-door-open"></i>
                                        <span>I Am Open</span>
                                    </button>
                                    <button class="vote-btn" data-vote="wonder" data-issue="{{ $issue->id }}">
                                        <i class="fas fa-question"></i>
                                        <span>I Wonder</span>
                                    </button>
                                    <button class="vote-btn" data-vote="disfavor" data-issue="{{ $issue->id }}">
                                        <i class="fas fa-times"></i>
                                        <span>I Disfavor</span>
                                    </button>
                                </div>

                                <div class="comment-section">
                                    @if($issue->discussion && $issue->discussion->posts && $issue->discussion->posts->count() > 0)
                                        <div class="recent-comments">
                                            @foreach($issue->discussion->posts->take(2) as $post)
                                                <div class="comment-item">
                                                    <div class="comment-avatar">
                                                        @if($post->author && $post->author->profile_pic && $post->author->profile_pic !== 'profile-placeholder.png')
                                                            <img src="{{ asset('images/' . $post->author->profile_pic) }}" alt="{{ $post->author->firstname }} {{ $post->author->lastname }}">
                                                        @else
                                                            <img src="{{ asset('images/profile-placeholder.png') }}" alt="User">
                                                        @endif
                                                    </div>
                                                    <div class="comment-content">
                                                        <div class="comment-author">{{ $post->author ? $post->author->firstname . ' ' . $post->author->lastname : 'Unknown User' }}</div>
                                                        <div class="comment-text">{{ $post->comment }}</div>
                                                        <div class="comment-time">{{ $post->created_at->diffForHumans() }}</div>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                    @endif

                                    <form class="comment-form" action="{{ route('issue.post', $issue->id) }}" method="POST">
                                        @csrf
                                        <input type="text" name="comment" class="comment-input" placeholder="Type Comment...">
                                        <button type="submit" class="comment-button">
                                            <i class="fas fa-arrow-right"></i>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    @endforeach
                @else
                    <div class="empty-state">
                        <p>No recent requests found.</p>
                        <a href="{{ route('issue.create') }}" class="btn btn-primary">Create Request</a>
                    </div>
                @endif
            </div>
        </div>

        <div class="activity-card">
            <div class="card-header">
                <h3 class="card-title">Recent Discussions</h3>
                <a href="{{ route('discussion.index') }}" class="btn btn-primary">View All</a>
            </div>
            <div class="card-body">
                @if(isset($recentDiscussions) && $recentDiscussions->count() > 0)
                    @foreach($recentDiscussions as $discussion)
                        <div class="activity-item">
                            <div class="activity-avatar">
                                @if($discussion->creator && $discussion->creator->profile_pic && $discussion->creator->profile_pic !== 'profile-placeholder.png')
                                    <img src="{{ asset('images/' . $discussion->creator->profile_pic) }}" alt="{{ $discussion->creator->firstname }} {{ $discussion->creator->lastname }}">
                                @else
                                    <img src="{{ asset('images/profile-placeholder.png') }}" alt="User">
                                @endif
                            </div>
                            <div class="activity-content">
                                <div class="activity-header">
                                    <h3 class="activity-title">{{ $discussion->title }}</h3>
                                    <span class="activity-time">{{ $discussion->created_at->diffForHumans() }}</span>
                                </div>
                                <p class="activity-group">
                                    Discussion by {{ $discussion->creator ? $discussion->creator->firstname . ' ' . $discussion->creator->lastname : 'Unknown User' }}
                                    in {{ $discussion->group ? $discussion->group->name : 'Unknown Group' }}
                                </p>
                                <p class="activity-description">
                                    {{ \Illuminate\Support\Str::limit($discussion->body ?? '', 300) }}
                                </p>

                                <div class="comment-section">
                                    @if($discussion->posts && $discussion->posts->count() > 0)
                                        <div class="recent-comments">
                                            @foreach($discussion->posts->take(2) as $post)
                                                <div class="comment-item">
                                                    <div class="comment-avatar">
                                                        @if($post->author && $post->author->profile_pic && $post->author->profile_pic !== 'profile-placeholder.png')
                                                            <img src="{{ asset('images/' . $post->author->profile_pic) }}" alt="{{ $post->author->firstname }} {{ $post->author->lastname }}">
                                                        @else
                                                            <img src="{{ asset('images/profile-placeholder.png') }}" alt="User">
                                                        @endif
                                                    </div>
                                                    <div class="comment-content">
                                                        <div class="comment-author">{{ $post->author ? $post->author->firstname . ' ' . $post->author->lastname : 'Unknown User' }}</div>
                                                        <div class="comment-text">{{ $post->comment }}</div>
                                                        <div class="comment-time">{{ $post->created_at->diffForHumans() }}</div>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                    @endif

                                    <form class="comment-form" action="{{ route('discussion.post', $discussion->id) }}" method="POST">
                                        @csrf
                                        <input type="text" name="comment" class="comment-input" placeholder="Type Comment...">
                                        <button type="submit" class="comment-button">
                                            <i class="fas fa-arrow-right"></i>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    @endforeach
                @else
                    <div class="empty-state">
                        <p>No recent discussions found.</p>
                        <a href="{{ route('discussion.create') }}" class="btn btn-primary">Start Discussion</a>
                    </div>
                @endif
            </div>
        </div>
    </div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle voting buttons
        const voteButtons = document.querySelectorAll('.vote-btn');

        voteButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();

                const voteType = this.dataset.vote;
                const issueId = this.dataset.issue;
                const buttonsGroup = this.parentElement;

                // Remove active class from all buttons in this group
                buttonsGroup.querySelectorAll('.vote-btn').forEach(btn => {
                    btn.classList.remove('active');
                });

                // Add active class to clicked button
                this.classList.add('active');

                // Here you would typically send an AJAX request to save the vote
                console.log('Vote:', voteType, 'for issue:', issueId);

                // Example AJAX call (uncomment when backend is ready):
                /*
                fetch(`/issue/${issueId}/vote`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        vote_type: voteType
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        console.log('Vote saved successfully');
                    }
                })
                .catch(error => {
                    console.error('Error saving vote:', error);
                });
                */
            });
        });

        // Handle comment forms
        const commentForms = document.querySelectorAll('.comment-form');

        commentForms.forEach(form => {
            form.addEventListener('submit', function(e) {
                e.preventDefault();

                const input = this.querySelector('.comment-input');
                const comment = input.value.trim();

                if (comment) {
                    // Submit the form normally for now
                    // In the future, this could be converted to AJAX
                    this.submit();
                } else {
                    // Show error message for empty comment
                    input.style.borderColor = '#EF4444';
                    setTimeout(() => {
                        input.style.borderColor = '#E5E7EB';
                    }, 2000);
                }
            });
        });

        // Add some visual feedback for comment inputs
        const commentInputs = document.querySelectorAll('.comment-input');

        commentInputs.forEach(input => {
            input.addEventListener('focus', function() {
                this.style.borderColor = '#5144A1';
            });

            input.addEventListener('blur', function() {
                if (!this.value.trim()) {
                    this.style.borderColor = '#E5E7EB';
                }
            });
        });
    });
</script>
@endsection
